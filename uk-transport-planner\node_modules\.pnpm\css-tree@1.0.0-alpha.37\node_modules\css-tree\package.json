{"name": "css-tree", "version": "1.0.0-alpha.37", "description": "CSSTree is a tool set to work with CSS, including fast detailed parser (string->AST), walker (AST traversal), generator (AST->string) and lexer (validation and matching) based on knowledge of spec and browser implementations", "author": "<PERSON> <<EMAIL>> (https://github.com/lahmatiy)", "license": "MIT", "repository": "csstree/csstree", "keywords": ["css", "ast", "tokenizer", "parser", "walker", "lexer", "generator", "utils", "syntax", "validation"], "main": "./lib/index", "browser": {"./data": "./dist/default-syntax.json"}, "scripts": {"build": "npm run gen:syntax && rollup --config && terser dist/csstree.js --compress --mangle -o dist/csstree.min.js", "lint": "eslint data lib scripts test && node scripts/review-syntax-patch --lint && node scripts/update-docs --lint", "lint-and-test": "npm run lint && npm test", "update:docs": "node scripts/update-docs", "gen:syntax": "node scripts/gen-syntax-data", "review:syntax-patch": "node scripts/review-syntax-patch", "test": "mocha --reporter progress", "coverage": "nyc npm test", "travis": "nyc npm run lint-and-test && npm run coveralls", "coveralls": "nyc report --reporter=text-lcov | coveralls", "prepublishOnly": "npm run build", "hydrogen": "node --trace-hydrogen --trace-phase=Z --trace-deopt --code-comments --hydrogen-track-positions --redirect-code-traces --redirect-code-traces-to=code.asm --trace_hydrogen_file=code.cfg --print-opt-code bin/parse --stat -o /dev/null"}, "dependencies": {"mdn-data": "2.0.4", "source-map": "^0.6.1"}, "devDependencies": {"coveralls": "^3.0.4", "eslint": "^6.3.0", "json-to-ast": "^2.1.0", "mocha": "^5.2.0", "nyc": "^14.1.1", "rollup": "^1.22.0", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-json": "^4.0.0", "rollup-plugin-node-resolve": "^5.2.0", "terser": "^4.3.4"}, "engines": {"node": ">=8.0.0"}, "files": ["data", "dist", "lib"]}