 # 英国地图路线规划和成本计算工具 - 需求文档

## 项目概述
开发一个基于Web的英国地图路线规划工具，支持氢能设施到港口的多模式运输路径规划，包括成本计算和碳排放分析。

## 核心功能需求

### 1. 地图显示
- 显示英国地图
- 标注氢能设施起始点（30个）
- 标注港口终点（11个）
- 标注铁路站点（36个）
- 支持多条路径同时显示
- 路径上显示计算结果

### 2. 筛选功能

#### 2.1 基础筛选
- **起始地选择**：下拉选择氢能设施
- **终点选择**：下拉选择港口

#### 2.2 运输模式筛选（Filter1）
- **Road（公路）**：
  - BEV（电动车）
  - HGV（柴油车）
- **Rail（铁路）**
- **Maritime（海运）**
- **多模式组合**：
  - Road + Rail
  - Road + Maritime  
  - Road + Rail + Maritime

#### 2.3 优化目标筛选（Filter2）
支持多选，选择后高亮显示数值最小的路径：
- **Distance（距离）**
- **Leadtime（运输时间）** - 排除海运
- **LCOT（运输成本）**
- **Carbon Emission（碳排放）**

#### 2.4 输入参数
- **Payload（载重）**：单位kg

### 3. 路径规划逻辑

#### 3.1 单模式运输
- **公路**：起始地 → 终点港口（直接路线）
- **铁路**：起始地 → 最近铁路站点 → 终点港口附近铁路站点 → 终点港口
- **海运**：起始地 → 最近港口 → 终点港口

#### 3.2 多模式运输
- **Road + Rail**：
  1. Hub → 最近铁路站（Station_A）- 公路
  2. Station_A → Station_B（港口附近铁路站）- 铁路
  3. Station_B → Port - 公路

- **Road + Maritime**：
  1. Hub → 最近港口（Port_A）- 公路
  2. Port_A → Port_B - 海运

- **Road + Rail + Maritime**：
  1. Hub → 最近铁路站A - 公路
  2. 铁路站A → 港口附近铁路站B - 铁路
  3. 铁路站B → 港口B - 公路
  4. 港口B → 终点港口C - 海运

### 4. 计算公式

#### 4.1 LCOT（运输成本）
```
LCOT = Total Cost / (Payload × Distance) = (Fuel Consumption Rate × Fuel Price × Distance) / (Payload × Distance)
```

**燃料消耗率和价格表**：
| 运输方式 | 类型 | 燃料消耗率 | 燃料价格 |
|---------|------|-----------|---------|
| 公路 | 电车BEV | 1.1 kWh/km | 18.5 P/KWh |
| 公路 | 柴油车HGV | 0.33 L/km | 137.5 pence/L |
| 铁路 | 柴油 | 4.7 L/trainkm | 137.5 pence/L |
| 海运 | 柴油 | 30 L/km | 80 pence/L |

#### 4.2 Carbon Emission（碳排放）
```
CE = D × W × EF
```
- D = 运输距离（km）
- W = 运输货物重量（t）
- EF = 碳排放因子（kg CO₂/t·km）

**碳排放因子表**：
| 运输方式 | EF (kg CO₂/t·km) |
|---------|------------------|
| 公路（柴油） | 0.1 |
| 公路（电） | 0.025 |
| 铁路 | 0.025 |
| 国内海运 | 0.0075 |

## 技术需求

### 1. API集成
- **Google Maps API**：用于铁路和海运路线规划
  - API Key: AIzaSyAELMbPo4yFD3tnuzwB7i_5iH4lnjBS-pg
- **PTV API**：用于公路路线规划
  - API Key: RVVfZDVlOWQ3ODNlOGJhNDc0NmI2YWI0NjU4ZWUxMjBjNzI6Y2Y2YmY1OTQtYmFlZS00MDBiLTk1NDktY2Y3NDE5YWNjNWNm
  - 文档：https://developer.myptv.com/en/documentation

### 2. 数据存储
- 氢能设施坐标数据（30个点位）
- 港口坐标数据（11个港口）
- 铁路站点坐标数据（36个站点）
- 海运距离数据（待提供）

### 3. 技术栈
- **前端**：React + Leaflet
- **地图**：Leaflet + OpenStreetMap
- **数据格式**：GeoJSON
- **样式**：CSS/SCSS

## 用户界面需求

### 1. 布局结构
- 左侧：筛选面板
- 右侧：地图显示区域
- 底部/侧边：路径结果展示

### 2. 交互需求
- 默认显示所有可能路径及计算结果
- 筛选器选择后高亮最优路径
- 支持多条路径对比显示
- 路径上显示关键计算数据

### 3. 显示需求
- 清晰的路径可视化
- 不同运输模式用不同颜色/样式
- 计算结果实时更新
- 响应式设计

## 开发优先级
1. 基础地图和数据点显示
2. 单模式路径规划（公路）
3. 成本和碳排放计算
4. 筛选功能实现
5. 多模式路径规划
6. UI优化和交互完善

## 注意事项
- 需要VPN访问Google API
- 代码需要详细注释
- 海运距离数据需要后续提供
- 不需要数据库，使用文件存储
- 工具仅供内部使用，无需发布部署
