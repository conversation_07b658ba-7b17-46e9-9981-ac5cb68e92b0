import { ExplorerBase } from './ExplorerBase';
import { CosmiconfigResult, ExplorerOptionsSync } from './types';
declare class ExplorerSync extends ExplorerBase<ExplorerOptionsSync> {
    constructor(options: ExplorerOptionsSync);
    searchSync(searchFrom?: string): CosmiconfigResult;
    private searchFromDirectorySync;
    private searchDirectorySync;
    private loadSearchPlaceSync;
    private loadFileContentSync;
    private createCosmiconfigResultSync;
    loadSync(filepath: string): CosmiconfigResult;
}
export { ExplorerSync };
//# sourceMappingURL=ExplorerSync.d.ts.map