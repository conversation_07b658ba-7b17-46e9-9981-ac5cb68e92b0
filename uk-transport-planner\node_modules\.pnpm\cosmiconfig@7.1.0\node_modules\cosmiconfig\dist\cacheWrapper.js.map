{"version": 3, "sources": ["../src/cacheWrapper.ts"], "names": ["cacheWrapper", "cache", "key", "fn", "cached", "get", "undefined", "result", "set", "cacheWrapperSync"], "mappings": ";;;;;;;;AAEA,eAAeA,YAAf,CACEC,KADF,EAEEC,GAFF,EAGEC,EAHF,EAI8B;AAC5B,QAAMC,MAAM,GAAGH,KAAK,CAACI,GAAN,CAAUH,GAAV,CAAf;;AACA,MAAIE,MAAM,KAAKE,SAAf,EAA0B;AACxB,WAAOF,MAAP;AACD;;AAED,QAAMG,MAAM,GAAG,MAAMJ,EAAE,EAAvB;AACAF,EAAAA,KAAK,CAACO,GAAN,CAAUN,GAAV,EAAeK,MAAf;AACA,SAAOA,MAAP;AACD;;AAED,SAASE,gBAAT,CACER,KADF,EAEEC,GAFF,EAGEC,EAHF,EAIqB;AACnB,QAAMC,MAAM,GAAGH,KAAK,CAACI,GAAN,CAAUH,GAAV,CAAf;;AACA,MAAIE,MAAM,KAAKE,SAAf,EAA0B;AACxB,WAAOF,MAAP;AACD;;AAED,QAAMG,MAAM,GAAGJ,EAAE,EAAjB;AACAF,EAAAA,KAAK,CAACO,GAAN,CAAUN,GAAV,EAAeK,MAAf;AACA,SAAOA,MAAP;AACD", "sourcesContent": ["import { <PERSON><PERSON>, CosmiconfigResult } from './types';\n\nasync function cacheWrapper(\n  cache: Cache,\n  key: string,\n  fn: () => Promise<CosmiconfigResult>,\n): Promise<CosmiconfigResult> {\n  const cached = cache.get(key);\n  if (cached !== undefined) {\n    return cached;\n  }\n\n  const result = await fn();\n  cache.set(key, result);\n  return result;\n}\n\nfunction cacheWrapperSync(\n  cache: Cache,\n  key: string,\n  fn: () => CosmiconfigResult,\n): CosmiconfigResult {\n  const cached = cache.get(key);\n  if (cached !== undefined) {\n    return cached;\n  }\n\n  const result = fn();\n  cache.set(key, result);\n  return result;\n}\n\nexport { cacheWrapper, cacheWrapperSync };\n"], "file": "cacheWrapper.js"}