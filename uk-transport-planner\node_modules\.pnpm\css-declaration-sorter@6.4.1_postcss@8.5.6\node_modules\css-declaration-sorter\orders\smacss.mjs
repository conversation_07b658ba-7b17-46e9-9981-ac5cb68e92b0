export const properties = [
  "all",
  "box-sizing",
  "contain",
  "contain-intrinsic-height",
  "contain-intrinsic-size",
  "contain-intrinsic-width",
  "container",
  "container-name",
  "container-type",
  "display",
  "appearance",
  "visibility",
  "content-visibility",
  "z-index",
  "paint-order",
  "position",
  "top",
  "right",
  "bottom",
  "left",
  "offset",
  "offset-anchor",
  "offset-distance",
  "offset-path",
  "offset-rotate",


  "grid",
  "grid-template-rows",
  "grid-template-columns",
  "grid-template-areas",
  "grid-auto-rows",
  "grid-auto-columns",
  "grid-auto-flow",
  "column-gap",
  "row-gap",
  "grid-area",
  "grid-row",
  "grid-row-start",
  "grid-row-end",
  "grid-column",
  "grid-column-start",
  "grid-column-end",
  "grid-template",
  "flex",
  "flex-grow",
  "flex-shrink",
  "flex-basis",
  "flex-direction",
  "flex-flow",
  "flex-wrap",
  "box-decoration-break",
  "place-content",
  "place-items",
  "place-self",
  "align-content",
  "align-items",
  "align-self",
  "justify-content",
  "justify-items",
  "justify-self",
  "order",
  "aspect-ratio",
  "width",
  "min-width",
  "max-width",
  "height",
  "min-height",
  "max-height",
  "-webkit-line-clamp",
  "-webkit-text-fill-color",
  "-webkit-text-stroke",
  "-webkit-text-stroke-color",
  "-webkit-text-stroke-width",
  "inline-size",
  "min-inline-size",
  "max-inline-size",
  "block-size",
  "min-block-size",
  "max-block-size",
  "margin",
  "margin-top",
  "margin-right",
  "margin-bottom",
  "margin-left",
  "margin-inline",
  "margin-inline-start",
  "margin-inline-end",
  "margin-block",
  "margin-block-start",
  "margin-block-end",
  "inset",
  "inset-block",
  "inset-block-end",
  "inset-block-start",
  "inset-inline",
  "inset-inline-end",
  "inset-inline-start",
  "padding",
  "padding-top",
  "padding-right",
  "padding-bottom",
  "padding-left",
  "padding-inline",
  "padding-inline-start",
  "padding-inline-end",
  "padding-block",
  "padding-block-start",
  "padding-block-end",
  "float",
  "clear",
  "overflow",
  "overflow-anchor",
  "overflow-block",
  "overflow-clip-margin",
  "overflow-inline",
  "overflow-x",
  "overflow-y",
  "overscroll-behavior",
  "overscroll-behavior-block",
  "overscroll-behavior-inline",
  "overscroll-behavior-x",
  "overscroll-behavior-y",
  "orphans",
  "gap",
  "columns",
  "column-fill",
  "column-rule",
  "column-rule-color",
  "column-rule-style",
  "column-rule-width",
  "column-span",
  "column-count",
  "column-width",
  "object-fit",
  "object-position",
  "transform",
  "transform-box",
  "transform-origin",
  "transform-style",
  "translate",
  "rotate",
  "scale",

  "border",
  "border-top",
  "border-right",
  "border-bottom",
  "border-left",
  "border-width",
  "border-top-width",
  "border-right-width",
  "border-bottom-width",
  "border-left-width",
  "border-style",
  "border-top-style",
  "border-right-style",
  "border-bottom-style",
  "border-left-style",
  "border-radius",
  "border-top-right-radius",
  "border-top-left-radius",
  "border-bottom-right-radius",
  "border-bottom-left-radius",
  "border-inline",
  "border-inline-color",
  "border-inline-style",
  "border-inline-width",
  "border-inline-start",
  "border-inline-start-color",
  "border-inline-start-style",
  "border-inline-start-width",
  "border-inline-end",
  "border-inline-end-color",
  "border-inline-end-style",
  "border-inline-end-width",
  "border-block",
  "border-block-color",
  "border-block-style",
  "border-block-width",
  "border-block-start",
  "border-block-start-color",
  "border-block-start-style",
  "border-block-start-width",
  "border-block-end",
  "border-block-end-color",
  "border-block-end-style",
  "border-block-end-width",
  "border-color",
  "border-image",
  "border-image-outset",
  "border-image-repeat",
  "border-image-slice",
  "border-image-source",
  "border-image-width",
  "border-top-color",
  "border-right-color",
  "border-bottom-color",
  "border-left-color",
  "border-collapse",
  "border-spacing",
  "border-start-start-radius",
  "border-start-end-radius",
  "border-end-start-radius",
  "border-end-end-radius",
  "outline",
  "outline-color",
  "outline-style",
  "outline-width",
  "outline-offset",

  "backdrop-filter",
  "backface-visibility",
  "background",
  "background-image",
  "background-position",
  "background-size",
  "background-repeat",
  "background-origin",
  "background-clip",
  "background-attachment",
  "background-color",
  "background-blend-mode",
  "background-position-x",
  "background-position-y",
  "box-shadow",
  "isolation",

  "content",
  "quotes",
  "hanging-punctuation",
  "color",
  "accent-color",
  "print-color-adjust",
  "forced-color-adjust",
  "color-scheme",
  "caret-color",
  "font",
  "font-style",
  "font-variant",
  "font-weight",
  "src",
  "font-stretch",
  "font-size",
  "size-adjust",
  "line-height",
  "font-family",
  "font-display",
  "font-kerning",
  "font-language-override",
  "font-optical-sizing",
  "font-palette",
  "font-size-adjust",
  "font-synthesis",
  "font-variant-alternates",
  "font-variant-caps",
  "font-variant-east-asian",
  "font-variant-emoji",
  "font-variant-ligatures",
  "font-variant-numeric",
  "font-variant-position",
  "font-variation-settings",
  "ascent-override",
  "descent-override",
  "line-gap-override",
  "hyphens",
  "hyphenate-character",
  "letter-spacing",
  "line-break",
  "list-style",
  "list-style-image",
  "list-style-position",
  "list-style-type",
  "direction",
  "text-align",
  "text-align-last",
  "text-decoration",
  "text-decoration-line",
  "text-decoration-style",
  "text-decoration-color",
  "text-decoration-thickness",
  "text-decoration-skip-ink",
  "text-emphasis",
  "text-emphasis-style",
  "text-emphasis-color",
  "text-emphasis-position",
  "text-indent",
  "text-justify",
  "text-underline-position",
  "text-underline-offset",
  "text-orientation",
  "text-overflow",
  "text-rendering",
  "text-shadow",
  "text-transform",
  "vertical-align",
  "white-space",
  "word-break",
  "word-spacing",
  "overflow-wrap",

  "animation",
  "animation-duration",
  "animation-timing-function",
  "animation-delay",
  "animation-iteration-count",
  "animation-direction",
  "animation-fill-mode",
  "animation-play-state",
  "animation-name",
  "mix-blend-mode",
  "break-before",
  "break-after",
  "break-inside",
  "page",
  "page-break-before",
  "page-break-after",
  "page-break-inside",
  "caption-side",
  "clip-path",
  "counter-increment",
  "counter-reset",
  "counter-set",
  "cursor",
  "empty-cells",
  "filter",
  "image-orientation",
  "image-rendering",
  "mask",
  "mask-border",
  "mask-border-outset",
  "mask-border-repeat",
  "mask-border-slice",
  "mask-border-source",
  "mask-border-width",
  "mask-clip",
  "mask-composite",
  "mask-image",
  "mask-mode",
  "mask-origin",
  "mask-position",
  "mask-repeat",
  "mask-size",
  "mask-type",
  "opacity",
  "perspective",
  "perspective-origin",
  "pointer-events",
  "resize",
  "scroll-behavior",
  "scroll-margin",
  "scroll-margin-block",
  "scroll-margin-block-end",
  "scroll-margin-block-start",
  "scroll-margin-bottom",
  "scroll-margin-inline",
  "scroll-margin-inline-end",
  "scroll-margin-inline-start",
  "scroll-margin-left",
  "scroll-margin-right",
  "scroll-margin-top",
  "scroll-padding",
  "scroll-padding-block",
  "scroll-padding-block-end",
  "scroll-padding-block-start",
  "scroll-padding-bottom",
  "scroll-padding-inline",
  "scroll-padding-inline-end",
  "scroll-padding-inline-start",
  "scroll-padding-left",
  "scroll-padding-right",
  "scroll-padding-top",
  "scroll-snap-align",
  "scroll-snap-stop",
  "scroll-snap-type",
  "scrollbar-color",
  "scrollbar-gutter",
  "scrollbar-width",
  "shape-image-threshold",
  "shape-margin",
  "shape-outside",
  "tab-size",
  "table-layout",
  "ruby-position",
  "text-combine-upright",
  "touch-action",
  "transition",
  "transition-delay",
  "transition-duration",
  "transition-property",
  "transition-timing-function",
  "will-change",
  "unicode-bidi",
  "unicode-range",
  "user-select",
  "widows",
  "writing-mode"
]
